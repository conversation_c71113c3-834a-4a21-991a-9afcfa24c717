# ApiAsyncTreeSelect 异步树形选择组件

## 功能特性

1. **异步加载**: 支持按需加载树形数据，提高性能
2. **灵活配置**: 支持自定义字段映射和参数配置
3. **兼容性**: 基于 Ant Design Vue TreeSelect 组件，保持 API 一致性
4. **搜索功能**: 支持树节点搜索过滤

## 基本用法

### 在表单 Schema 中使用

```typescript
// schemas.ts
import { getCategoryList } from '#/api/iot/category';

export const formSchemas: VbenFormSchema[] = [
  {
    fieldName: 'parentId',
    label: '父分类',
    component: 'ApiAsyncTreeSelect',
    componentProps: {
      api: getCategoryList,
      resultField: 'data.data',
      labelField: 'categoryName',
      valueField: 'id',
      parentKeyField: 'parentId',
      apiParentKey: 'parentId', // API 请求中父ID参数的名称
      rootParentValue: null, // 根节点的父ID值
      placeholder: '请选择父分类',
      allowClear: true,
      showSearch: true,
      defaultValue: {
        label: '根分类',
        value: 0,
      },
    },
    defaultValue: 0,
  },
];
```

### 使用模型动态获取父ID值

```typescript
// 当需要从表单模型中动态获取父ID值时
export const formSchemas: VbenFormSchema[] = [
  {
    fieldName: 'categoryId',
    label: '子分类',
    component: 'ApiAsyncTreeSelect',
    componentProps: {
      api: getCategoryList,
      resultField: 'data.data',
      labelField: 'categoryName',
      valueField: 'id',
      apiParentKey: 'parentId', // API 请求参数名
      model: formModel, // 表单模型对象
      modelParentKey: 'parentCategoryId', // 从模型中获取父ID的字段名
      placeholder: '请选择子分类',
      allowClear: true,
    },
  },
];
```

### 在 Vue 组件中直接使用

```vue
<template>
  <ApiAsyncTreeSelect
    v-model:value="selectedValue"
    :api="getCategoryList"
    result-field="data.data"
    label-field="categoryName"
    value-field="id"
    parent-key-field="parentId"
    :root-parent-value="null"
    placeholder="请选择分类"
    show-search
    allow-clear
    @options-change="handleOptionsChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import { ApiAsyncTreeSelect } from '#/components/form';
import { getCategoryList } from '#/api/iot/category';

const selectedValue = ref();

function handleOptionsChange(options) {
  console.log('Tree options changed:', options);
}
</script>
```

## Props 配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `value` | `SelectValue` | `undefined` | 选中的值 |
| `api` | `Function` | `undefined` | 异步加载数据的 API 函数 |
| `params` | `Object` | `undefined` | API 请求的额外参数 |
| `immediate` | `Boolean` | `true` | 是否立即加载根节点数据 |
| `resultField` | `String` | `''` | API 响应中数据字段的路径 |
| `labelField` | `String` | `'label'` | 显示文本的字段名 |
| `valueField` | `String` | `'value'` | 值的字段名 |
| `parentKeyField` | `String` | `'parentId'` | 父节点ID的字段名 |
| `childrenKeyField` | `String` | `'children'` | 子节点数组的字段名 |
| `isLeafField` | `String` | `'isLeaf'` | 是否为叶子节点的字段名 |
| `multiple` | `Boolean` | `false` | 是否支持多选 |
| `showSearch` | `Boolean` | `false` | 是否显示搜索框 |
| `loadData` | `Boolean` | `true` | 是否启用异步加载 |
| `rootParentValue` | `String\|Number` | `null` | 根节点的父ID值 |
| `apiParentKey` | `String` | `'parentId'` | API 请求中父ID参数的名称 |
| `model` | `Object` | `undefined` | 表单模型对象，用于动态获取参数值 |
| `modelParentKey` | `String` | `'parentId'` | 从模型中获取父ID值的字段名 |
| `defaultValue` | `Object` | `undefined` | 默认选项（如"根分类"） |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:value` | `value` | 选中值变化时触发 |
| `optionsChange` | `options` | 选项数据变化时触发 |

## API 数据格式要求

### 请求参数

API 函数会接收包含父ID参数的对象，参数名由 `apiParentKey` 指定：

```typescript
// 加载根节点时（使用 apiParentKey 作为参数名）
{
  parentId: null, // rootParentValue 的值，或从 model[modelParentKey] 获取
  ...otherParams
}

// 加载子节点时
{
  parentId: 123, // 父节点的 value
  ...otherParams
}

// 示例：自定义 API 参数名
// 如果设置 apiParentKey: 'parent_id'，则请求参数为：
{
  parent_id: null,
  ...otherParams
}
```

### 响应数据格式

```typescript
// 直接返回数组
[
  {
    id: 1,
    categoryName: '电子产品',
    parentId: null,
    isLeaf: false, // 可选，用于标识是否为叶子节点
  },
  // ...
]

// 或包装在对象中
{
  data: {
    data: [
      {
        id: 1,
        categoryName: '电子产品',
        parentId: null,
        isLeaf: false,
      },
      // ...
    ]
  }
}
```

## 使用场景示例

### 1. 部门选择器

```typescript
{
  fieldName: 'departmentId',
  label: '所属部门',
  component: 'ApiAsyncTreeSelect',
  componentProps: {
    api: getDepartmentList,
    resultField: 'data',
    labelField: 'name',
    valueField: 'id',
    parentKeyField: 'parentId',
    rootParentValue: 0,
    placeholder: '请选择部门',
  },
}
```

### 2. 地区选择器

```typescript
{
  fieldName: 'regionId',
  label: '所在地区',
  component: 'ApiAsyncTreeSelect',
  componentProps: {
    api: getRegionList,
    resultField: 'data.list',
    labelField: 'regionName',
    valueField: 'regionCode',
    parentKeyField: 'parentCode',
    rootParentValue: '000000',
    placeholder: '请选择地区',
    showSearch: true,
  },
}
```

### 3. 菜单选择器

```typescript
{
  fieldName: 'parentMenuId',
  label: '父级菜单',
  component: 'ApiAsyncTreeSelect',
  componentProps: {
    api: getMenuList,
    labelField: 'menuName',
    valueField: 'menuId',
    apiParentKey: 'parentId',
    rootParentValue: null,
    defaultValue: {
      label: '根菜单',
      value: 0,
    },
  },
}
```

### 4. 动态父ID场景

```typescript
// 表单模型
const formModel = reactive({
  warehouseId: 1,
  categoryId: null,
  // ... 其他字段
});

// Schema 配置
{
  fieldName: 'categoryId',
  label: '商品分类',
  component: 'ApiAsyncTreeSelect',
  componentProps: {
    api: getCategoryList,
    resultField: 'data.list',
    labelField: 'categoryName',
    valueField: 'id',
    apiParentKey: 'warehouse_id', // API 参数名为 warehouse_id
    model: formModel, // 传入表单模型
    modelParentKey: 'warehouseId', // 从 formModel.warehouseId 获取值
    placeholder: '请选择分类',
  },
}

// 最终的 API 请求参数会是：
// { warehouse_id: 1, ...otherParams }
```

## 注意事项

1. **性能优化**: 组件采用按需加载策略，只有在展开节点时才会加载子节点数据
2. **错误处理**: 当 API 请求失败时，会自动将节点标记为叶子节点
3. **数据缓存**: 已加载的节点数据会被缓存，避免重复请求
4. **字段映射**: 确保 API 返回的数据字段与配置的字段名匹配
5. **兼容性**: 支持所有 Ant Design Vue TreeSelect 的原生属性和事件
