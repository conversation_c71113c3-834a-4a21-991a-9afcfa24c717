<template>
  <div class="p-6">
    <h2 class="text-xl font-bold mb-4">ApiAsyncTreeSelect 正确用法示例</h2>
    
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">基本用法</h3>
      <div class="w-80">
        <ApiAsyncTreeSelect
          v-model:value="selectedValue"
          :api="categoryApi"
          result-field="data"
          label-field="name"
          value-field="id"
          api-parent-key="parentId"
          :root-parent-value="null"
          placeholder="请选择分类"
          allow-clear
        />
      </div>
      <p class="mt-2 text-sm">选中值: {{ selectedValue }}</p>
    </div>

    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">API 数据格式要求</h3>
      <div class="bg-gray-50 p-4 rounded">
        <h4 class="font-medium mb-2">请求参数:</h4>
        <pre class="text-xs mb-4">{{ JSON.stringify(sampleRequest, null, 2) }}</pre>
        
        <h4 class="font-medium mb-2">响应格式:</h4>
        <pre class="text-xs">{{ JSON.stringify(sampleResponse, null, 2) }}</pre>
      </div>
    </div>

    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">重要说明</h3>
      <div class="bg-yellow-50 border border-yellow-200 p-4 rounded">
        <ul class="text-sm space-y-1">
          <li>• 根据 Ant Design Vue 官方文档，loadData 会将子节点添加到全局 treeData 数组中</li>
          <li>• 节点必须包含 dataRef 属性，用于存储原始数据</li>
          <li>• 只有 isLeaf !== true 的节点才能展开加载子节点</li>
          <li>• API 返回的数据会被自动转换为 TreeSelect 需要的格式</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ApiAsyncTreeSelect } from './index';

const selectedValue = ref();

// 示例请求参数
const sampleRequest = {
  parentId: null, // 或具体的父节点ID
  // 其他自定义参数...
};

// 示例响应格式
const sampleResponse = {
  data: [
    {
      id: 1,
      name: '电子产品',
      parentId: null,
      isLeaf: false, // 可以展开
    },
    {
      id: 2,
      name: '图书音像',
      parentId: null,
      isLeaf: true, // 叶子节点，不能展开
    }
  ]
};

// 模拟 API 函数
async function categoryApi(params: any) {
  console.log('API 请求参数:', params);
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 模拟数据
  const mockData = {
    null: [
      { id: 1, name: '电子产品', parentId: null, isLeaf: false },
      { id: 2, name: '服装鞋帽', parentId: null, isLeaf: false },
      { id: 3, name: '图书音像', parentId: null, isLeaf: true },
    ],
    1: [
      { id: 11, name: '手机通讯', parentId: 1, isLeaf: false },
      { id: 12, name: '电脑办公', parentId: 1, isLeaf: true },
    ],
    2: [
      { id: 21, name: '男装', parentId: 2, isLeaf: true },
      { id: 22, name: '女装', parentId: 2, isLeaf: true },
    ],
    11: [
      { id: 111, name: '智能手机', parentId: 11, isLeaf: true },
      { id: 112, name: '老人机', parentId: 11, isLeaf: true },
    ],
  };
  
  const parentId = params.parentId;
  const data = mockData[parentId as keyof typeof mockData] || [];
  
  return {
    data,
    success: true,
  };
}
</script>

<style scoped>
pre {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
