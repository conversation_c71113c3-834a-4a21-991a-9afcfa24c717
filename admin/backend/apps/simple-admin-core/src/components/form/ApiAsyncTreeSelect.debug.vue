<template>
  <div class="p-6">
    <h2 class="text-xl font-bold mb-4">ApiAsyncTreeSelect 调试页面</h2>
    
    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">组件测试</h3>
      <div class="w-80">
        <ApiAsyncTreeSelect
          v-model:value="selectedValue"
          :api="debugApi"
          result-field="data"
          label-field="name"
          value-field="id"
          api-parent-key="parentId"
          :root-parent-value="null"
          placeholder="请选择节点"
          allow-clear
          @options-change="handleOptionsChange"
        />
      </div>
      <p class="mt-2 text-sm">选中值: {{ selectedValue }}</p>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">调试信息</h3>
      <div class="bg-gray-100 p-4 rounded max-h-60 overflow-y-auto">
        <div v-for="(log, index) in debugLogs" :key="index" class="mb-1 text-xs">
          <span class="text-blue-600">{{ log.time }}</span>
          <span class="ml-2">{{ log.message }}</span>
          <pre v-if="log.data" class="mt-1 text-gray-600">{{ JSON.stringify(log.data, null, 2) }}</pre>
        </div>
      </div>
      <button 
        @click="debugLogs = []" 
        class="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm"
      >
        清空日志
      </button>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">当前树数据</h3>
      <div class="bg-gray-50 p-4 rounded max-h-40 overflow-y-auto">
        <pre class="text-xs">{{ JSON.stringify(currentTreeData, null, 2) }}</pre>
      </div>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">测试说明</h3>
      <ul class="text-sm text-gray-600 list-disc list-inside">
        <li>打开浏览器开发者工具查看控制台日志</li>
        <li>点击树节点的展开按钮时会触发 onLoadData</li>
        <li>检查 API 请求参数和响应数据</li>
        <li>确认子节点是否正确添加到树中</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ApiAsyncTreeSelect } from './index';

const selectedValue = ref();
const debugLogs = ref<Array<{ time: string; message: string; data?: any }>>([]);
const currentTreeData = ref([]);

// 添加调试日志
function addLog(message: string, data?: any) {
  debugLogs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    data,
  });
}

// 模拟数据
const mockData = {
  null: [
    { id: 1, name: '根节点1', parentId: null, isLeaf: false },
    { id: 2, name: '根节点2', parentId: null, isLeaf: false },
    { id: 3, name: '叶子节点', parentId: null, isLeaf: true },
  ],
  1: [
    { id: 11, name: '子节点1-1', parentId: 1, isLeaf: true },
    { id: 12, name: '子节点1-2', parentId: 1, isLeaf: false },
  ],
  2: [
    { id: 21, name: '子节点2-1', parentId: 2, isLeaf: true },
    { id: 22, name: '子节点2-2', parentId: 2, isLeaf: true },
  ],
  12: [
    { id: 121, name: '子节点1-2-1', parentId: 12, isLeaf: true },
    { id: 122, name: '子节点1-2-2', parentId: 12, isLeaf: true },
  ],
};

// 调试用的 API 函数
async function debugApi(params: any) {
  addLog('API 调用开始', params);
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const parentId = params.parentId;
  const data = mockData[parentId as keyof typeof mockData] || [];
  
  const response = {
    data,
    success: true,
    message: 'success',
  };
  
  addLog('API 调用完成', response);
  
  return response;
}

// 处理选项变化
function handleOptionsChange(options: any) {
  addLog('选项数据变化', options);
  currentTreeData.value = options;
}
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
