<template>
  <div class="p-6">
    <h2 class="text-xl font-bold mb-4">ApiAsyncTreeSelect 调试页面</h2>
    
    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">组件测试</h3>
      <div class="w-80">
        <ApiAsyncTreeSelect
          v-model:value="selectedValue"
          :api="debugApi"
          result-field="data"
          label-field="name"
          value-field="id"
          api-parent-key="parentId"
          :root-parent-value="null"
          placeholder="请选择节点"
          allow-clear
          @options-change="handleOptionsChange"
        />
      </div>
      <p class="mt-2 text-sm">选中值: {{ selectedValue }}</p>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">调试信息</h3>
      <div class="bg-gray-100 p-4 rounded max-h-60 overflow-y-auto">
        <div v-for="(log, index) in debugLogs" :key="index" class="mb-1 text-xs">
          <span class="text-blue-600">{{ log.time }}</span>
          <span class="ml-2">{{ log.message }}</span>
          <pre v-if="log.data" class="mt-1 text-gray-600">{{ JSON.stringify(log.data, null, 2) }}</pre>
        </div>
      </div>
      <button 
        @click="debugLogs = []" 
        class="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm"
      >
        清空日志
      </button>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">测试说明</h3>
      <ul class="text-sm text-gray-600 list-disc list-inside">
        <li>打开浏览器开发者工具查看控制台日志</li>
        <li>点击树节点的展开按钮时会触发 onLoadData</li>
        <li>检查 API 请求参数和响应数据</li>
        <li>确认子节点是否正确添加到全局 treeData 中</li>
        <li>注意：只有 isLeaf !== true 的节点才能展开</li>
        <li>根据 Ant Design Vue 官方文档，子节点会被添加到全局数据数组中</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ApiAsyncTreeSelect } from './index';

const selectedValue = ref();
const debugLogs = ref<Array<{ time: string; message: string; data?: any }>>([]);

// 添加调试日志
function addLog(message: string, data?: any) {
  debugLogs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    data,
  });
}

// 模拟数据 - 注意：不设置 isLeaf 或设置为 false 的节点可以异步加载
const mockData = {
  null: [
    { id: 1, name: '根节点1', parentId: null }, // 不设置 isLeaf，默认可以展开
    { id: 2, name: '根节点2', parentId: null }, // 不设置 isLeaf，默认可以展开
    { id: 3, name: '叶子节点', parentId: null, isLeaf: true }, // 明确标记为叶子节点
  ],
  1: [
    { id: 11, name: '子节点1-1', parentId: 1, isLeaf: true }, // 叶子节点
    { id: 12, name: '子节点1-2', parentId: 1 }, // 可以继续展开
  ],
  2: [
    { id: 21, name: '子节点2-1', parentId: 2, isLeaf: true },
    { id: 22, name: '子节点2-2', parentId: 2, isLeaf: true },
  ],
  12: [
    { id: 121, name: '子节点1-2-1', parentId: 12, isLeaf: true },
    { id: 122, name: '子节点1-2-2', parentId: 12, isLeaf: true },
  ],
};

// 调试用的 API 函数
async function debugApi(params: any) {
  addLog('API 调用开始', params);
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const parentId = params.parentId;
  const data = mockData[parentId as keyof typeof mockData] || [];
  
  const response = {
    data,
    success: true,
    message: 'success',
  };
  
  addLog('API 调用完成', response);
  
  return response;
}

// 处理选项变化
function handleOptionsChange(options: any) {
  addLog('选项数据变化', options);
}
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
