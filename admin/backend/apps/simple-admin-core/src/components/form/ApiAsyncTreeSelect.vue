<script lang="ts" setup>
import type { Recordable } from '@vben/types';
import type { SelectValue } from 'ant-design-vue/es/select';
import type { PropType } from 'vue';

import { get } from '#/utils/object';
import { $t } from '@vben/locales';
import { useVModel } from '@vueuse/core';
import { TreeSelect } from 'ant-design-vue';
import { isArray, isFunction } from 'remeda';
import { onMounted, ref, unref, watch } from 'vue';

interface TreeNode {
  label: string;
  value: string | number;
  isLeaf?: boolean;
  children?: TreeNode[];
  pId?: string | number;
  dataRef?: any;
  [key: string]: any;
}

const props = defineProps({
  value: {
    type: [Array, Object, String, Number] as PropType<SelectValue>,
    default: undefined,
  },
  api: {
    type: Function as Function as PropType<(arg?: any) => Promise<any>>,
    default: undefined,
  },
  params: { type: Object, default: undefined },
  immediate: { type: Boolean, default: true },
  resultField: {
    type: String,
    default: '',
  },
  labelField: {
    type: String,
    default: 'label',
  },
  valueField: {
    type: String,
    default: 'value',
  },
  parentKeyField: {
    type: String,
    default: 'parentId',
  },
  childrenKeyField: {
    type: String,
    default: 'children',
  },
  isLeafField: {
    type: String,
    default: 'isLeaf',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  defaultValue: { type: Object, default: undefined },
  // search
  showSearch: {
    type: Boolean,
    default: false,
  },
  treeNodeFilterProp: {
    type: String,
    default: 'label',
  },
  // 异步加载相关配置
  loadData: {
    type: Boolean,
    default: true,
  },
  rootParentValue: {
    type: [String, Number],
    default: null,
  },
  // API 参数配置
  apiParentKey: {
    type: String,
    default: 'parentId',
  },
});

const emits = defineEmits(['update:value', 'optionsChange']);
const state = useVModel(props, 'value', emits, {
  defaultValue: props.value,
  passive: true,
});

const treeData = ref<TreeNode[]>([]);
const isFirstLoaded = ref<Boolean>(false);
const loading = ref(false);

watch(
  () => props.params,
  () => {
    !unref(isFirstLoaded) && fetchRootData();
  },
  { deep: true },
);

watch(
  () => props.immediate,
  (v) => {
    v && !isFirstLoaded.value && fetchRootData();
  },
);



onMounted(() => {
  props.immediate && fetchRootData();
});

// 获取根节点的父ID值
function getRootParentValue() {
  return props.rootParentValue;
}

// 获取根节点数据
async function fetchRootData() {
  const { api } = props;
  if (!api || !isFunction(api)) return;

  loading.value = true;
  treeData.value = [];
  let result;

  try {
    // 构建根节点查询参数
    const rootParams = {
      ...props.params,
      [props.apiParentKey]: getRootParentValue(),
    };
    result = await api(rootParams);
  } catch (error) {
    console.error('Failed to fetch root data:', error);
  }

  loading.value = false;
  if (!result) return;

  if (!isArray(result)) {
    result = get(result, props.resultField);
  }

  treeData.value = buildTreeNodes(result); // 根节点不需要 parentValue

  // 添加默认值
  if (props.defaultValue) {
    const defaultNode = {
      ...props.defaultValue,
      dataRef: { ...props.defaultValue },
    } as TreeNode;
    treeData.value.unshift(defaultNode);
  }

  emits('optionsChange', treeData.value);
  isFirstLoaded.value = true;
}

// 异步加载子节点数据
function onLoadData(treeNode: any): Promise<void> {
  return new Promise(async (resolve, reject) => {

    const { api } = props;
    if (!api || !isFunction(api)) {
      console.log('No API function provided');
      resolve();
      return;
    }

    try {
      console.log('treeNode:', treeNode)
      // 从 treeNode 获取节点值，优先使用 dataRef 中的数据
      const nodeValue = treeNode.dataRef?.value || treeNode.value;
      console.log('onLoadData called with nodeValue:', nodeValue);

      // 构建子节点查询参数
      const childParams = {
        ...props.params,
        [props.apiParentKey]: nodeValue,
      };

      console.log('Loading child data with params:', childParams);
      const result = await api(childParams);
      console.log('API response:', result);

      let childrenData = result;

      if (!isArray(result)) {
        childrenData = get(result, props.resultField);
        console.log('Extracted data from result field:', childrenData);
      }

      if (childrenData && isArray(childrenData) && childrenData.length > 0) {
        const childNodes = buildTreeNodes(childrenData, nodeValue);
        console.log('Built child nodes:', childNodes);

        // 根据官方文档，直接修改全局 treeData 数组
        // 使用 treeDataSimpleMode 时，通过 pId 属性建立父子关系
        treeData.value = treeData.value.concat(childNodes);

        console.log('Updated treeData:', treeData.value);
      } else {
        console.log('No children found');
      }

      resolve(true);
    } catch (error) {
      console.error('Failed to load child data:', error);
      reject(error);
    }
  });
}

// 构建树节点数据
function buildTreeNodes(data: any[], parentValue?: any): TreeNode[] {
  if (!data || !isArray(data)) return [];

  return data.map((item) => {
    const nodeValue = item[props.valueField] || item.value;
    const nodeLabel = item[props.labelField] || item.label;

    const node: TreeNode = {
      label: nodeLabel,
      value: nodeValue,
      // 添加 dataRef 属性，这是 Ant Design Vue 需要的
      dataRef: {
        ...item,
        value: nodeValue,
        label: nodeLabel,
      },
    };

    // 处理 isLeaf 属性
    if (item[props.isLeafField] !== undefined) {
      node.isLeaf = item[props.isLeafField];
    } else {
      // 默认为非叶子节点，允许异步加载
      node.isLeaf = false;
    }

    // 如果有父节点值，设置 pId 属性（用于扁平化数据结构）
    if (parentValue !== undefined) {
      node.pId = parentValue;
      node.dataRef.pId = parentValue;
    }

    return node;
  });
}




</script>

<template>
  <TreeSelect
    v-bind="$attrs"
    v-model:value="state"
    :load-data="props.loadData ? onLoadData : undefined"
    :loading="loading"
    :multiple="$props.multiple"
    :placeholder="$t('common.chooseText')"
    :show-search="props.showSearch"
    :tree-data="treeData"
    :tree-node-filter-prop="props.treeNodeFilterProp"
    tree-data-simple-mode
    class="w-full"
    @dropdown-visible-change="(open) => open && !isFirstLoaded && fetchRootData()"
  />
</template>
