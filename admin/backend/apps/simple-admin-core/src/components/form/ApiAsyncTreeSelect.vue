<script lang="ts" setup>
import type { Recordable } from '@vben/types';
import type { SelectValue } from 'ant-design-vue/es/select';
import type { PropType } from 'vue';

import { get } from '#/utils/object';
import { $t } from '@vben/locales';
import { useVModel } from '@vueuse/core';
import { TreeSelect } from 'ant-design-vue';
import { isArray, isFunction } from 'remeda';
import { onMounted, ref, unref, watch } from 'vue';

interface TreeNode {
  label: string;
  value: string | number;
  isLeaf?: boolean;
  children?: TreeNode[];
  pId?: string | number;
  dataRef?: any;
  [key: string]: any;
}

const props = defineProps({
  value: {
    type: [Array, Object, String, Number] as PropType<SelectValue>,
    default: undefined,
  },
  api: {
    type: Function as Function as PropType<(arg?: any) => Promise<any>>,
    default: undefined,
  },
  params: { type: Object, default: undefined },
  immediate: { type: Boolean, default: true },
  resultField: {
    type: String,
    default: '',
  },
  labelField: {
    type: String,
    default: 'label',
  },
  valueField: {
    type: String,
    default: 'value',
  },
  parentKeyField: {
    type: String,
    default: 'parentId',
  },
  childrenKeyField: {
    type: String,
    default: 'children',
  },
  isLeafField: {
    type: String,
    default: 'isLeaf',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  defaultValue: { type: Object, default: undefined },
  // search
  showSearch: {
    type: Boolean,
    default: false,
  },
  treeNodeFilterProp: {
    type: String,
    default: 'label',
  },
  // 异步加载相关配置
  loadData: {
    type: Boolean,
    default: true,
  },
  rootParentValue: {
    type: [String, Number],
    default: null,
  },
  // API 参数配置
  apiParentKey: {
    type: String,
    default: 'parentId',
  },
});

const emits = defineEmits(['update:value', 'optionsChange']);
const state = useVModel(props, 'value', emits, {
  defaultValue: props.value,
  passive: true,
});

const treeData = ref<TreeNode[]>([]);
const isFirstLoaded = ref<Boolean>(false);
const loading = ref(false);

watch(
  () => props.params,
  () => {
    !unref(isFirstLoaded) && fetchRootData();
  },
  { deep: true },
);

watch(
  () => props.immediate,
  (v) => {
    v && !isFirstLoaded.value && fetchRootData();
  },
);



onMounted(() => {
  console.log('ApiAsyncTreeSelect mounted');
  console.log('Props:', {
    immediate: props.immediate,
    api: !!props.api,
    apiParentKey: props.apiParentKey,
    rootParentValue: props.rootParentValue,
    resultField: props.resultField,
    labelField: props.labelField,
    valueField: props.valueField,
  });

  if (props.immediate) {
    console.log('Calling fetchRootData from onMounted');
    fetchRootData();
  } else {
    console.log('immediate is false, not calling fetchRootData');
  }
});

// 获取根节点的父ID值
function getRootParentValue() {
  return props.rootParentValue;
}

// 获取根节点数据
async function fetchRootData() {
  console.log('fetchRootData called');
  const { api } = props;
  if (!api || !isFunction(api)) {
    console.log('No API function provided for root data');
    return;
  }

  loading.value = true;
  treeData.value = [];
  let result;

  try {
    // 构建根节点查询参数
    const rootParams = {
      ...props.params,
      [props.apiParentKey]: getRootParentValue(),
    };
    console.log('Fetching root data with params:', rootParams);
    result = await api(rootParams);
    console.log('Root API response:', result);
  } catch (error) {
    console.error('Failed to fetch root data:', error);
  }

  loading.value = false;
  if (!result) {
    console.log('No result from root API');
    return;
  }

  if (!isArray(result)) {
    result = get(result, props.resultField);
    console.log('Extracted root data from result field:', result);
  }

  const rootNodes = buildTreeNodes(result); // 根节点不需要 parentValue
  console.log('Built root nodes:', rootNodes);

  treeData.value = rootNodes;

  // 添加默认值
  if (props.defaultValue) {
    const defaultNode = {
      ...props.defaultValue,
      dataRef: { ...props.defaultValue },
    } as TreeNode;
    treeData.value.unshift(defaultNode);
    console.log('Added default value:', defaultNode);
  }

  console.log('Final treeData:', treeData.value);
  emits('optionsChange', treeData.value);
  isFirstLoaded.value = true;
}

// 异步加载子节点数据
function onLoadData(treeNode: any): Promise<void> {
  return new Promise(async (resolve, reject) => {

    const { api } = props;
    if (!api || !isFunction(api)) {
      console.log('No API function provided');
      resolve();
      return;
    }

    try {
      console.log('treeNode:', treeNode)
      // 从 treeNode 获取节点值，优先使用 dataRef 中的数据
      const nodeValue = treeNode.dataRef?.value || treeNode.value;
      console.log('onLoadData called with nodeValue:', nodeValue);

      // 构建子节点查询参数
      const childParams = {
        ...props.params,
        [props.apiParentKey]: nodeValue,
      };

      console.log('Loading child data with params:', childParams);
      const result = await api(childParams);
      console.log('API response:', result);

      let childrenData = result;

      if (!isArray(result)) {
        childrenData = get(result, props.resultField);
        console.log('Extracted data from result field:', childrenData);
      }

      if (childrenData && isArray(childrenData) && childrenData.length > 0) {
        const childNodes = buildTreeNodes(childrenData);
        console.log('Built child nodes:', childNodes);

        // 在 treeData 中找到对应的节点并设置 children
        updateNodeChildren(nodeValue, childNodes);

        console.log('Updated treeData with children:', treeData.value);
      } else {
        console.log('No children found');
        // 设置空数组表示已加载但无子节点
        updateNodeChildren(nodeValue, []);
      }

      resolve(true);
    } catch (error) {
      console.error('Failed to load child data:', error);
      reject(error);
    }
  });
}

// 在 treeData 中找到指定节点并更新其 children
function updateNodeChildren(nodeValue: any, children: TreeNode[]) {
  function findAndUpdate(nodes: TreeNode[]): boolean {
    for (const node of nodes) {
      if (node.value === nodeValue) {
        // 找到目标节点，设置 children
        node.children = children;
        console.log(`Updated node ${nodeValue} children:`, children);
        return true;
      }
      // 递归查找子节点
      if (node.children && findAndUpdate(node.children)) {
        return true;
      }
    }
    return false;
  }

  const found = findAndUpdate(treeData.value);
  if (found) {
    // 强制触发响应式更新
    treeData.value = [...treeData.value];
  } else {
    console.warn(`Node with value ${nodeValue} not found in treeData`);
  }
}

// 构建树节点数据
function buildTreeNodes(data: any[]): TreeNode[] {
  if (!data || !isArray(data)) return [];

  return data.map((item) => {
    const nodeValue = item[props.valueField] || item.value;
    const nodeLabel = item[props.labelField] || item.label;

    const node: TreeNode = {
      label: nodeLabel,
      value: nodeValue,
      // 添加 dataRef 属性，这是 Ant Design Vue 需要的
      dataRef: {
        ...item,
        value: nodeValue,
        label: nodeLabel,
      },
    };

    // 处理 isLeaf 属性
    if (item[props.isLeafField] !== undefined) {
      node.isLeaf = item[props.isLeafField];
    } else {
      // 默认为非叶子节点，允许异步加载
      node.isLeaf = false;
    }

    return node;
  });
}




</script>

<template>
  <TreeSelect
    v-bind="$attrs"
    v-model:value="state"
    :load-data="props.loadData ? onLoadData : undefined"
    :loading="loading"
    :multiple="$props.multiple"
    :placeholder="$t('common.chooseText')"
    :show-search="props.showSearch"
    :tree-data="treeData"
    :tree-node-filter-prop="props.treeNodeFilterProp"
    class="w-full"
    @dropdown-visible-change="(open) => open && !isFirstLoaded && fetchRootData()"
  />
</template>
