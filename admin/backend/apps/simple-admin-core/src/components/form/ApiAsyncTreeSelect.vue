<script lang="ts" setup>
import type { Recordable } from '@vben/types';
import type { SelectValue } from 'ant-design-vue/es/select';
import type { PropType } from 'vue';

import { get } from '#/utils/object';
import { $t } from '@vben/locales';
import { useVModel } from '@vueuse/core';
import { TreeSelect } from 'ant-design-vue';
import { isArray, isFunction } from 'remeda';
import { onMounted, ref, unref, watch } from 'vue';

interface TreeNode {
  label: string;
  value: string | number;
  isLeaf?: boolean;
  children?: TreeNode[];
  [key: string]: any;
}

const props = defineProps({
  value: {
    type: [Array, Object, String, Number] as PropType<SelectValue>,
    default: undefined,
  },
  api: {
    type: Function as Function as PropType<(arg?: any) => Promise<any>>,
    default: undefined,
  },
  params: { type: Object, default: undefined },
  immediate: { type: Boolean, default: true },
  resultField: {
    type: String,
    default: '',
  },
  labelField: {
    type: String,
    default: 'label',
  },
  valueField: {
    type: String,
    default: 'value',
  },
  parentKeyField: {
    type: String,
    default: 'parentId',
  },
  childrenKeyField: {
    type: String,
    default: 'children',
  },
  isLeafField: {
    type: String,
    default: 'isLeaf',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  defaultValue: { type: Object, default: undefined },
  // search
  showSearch: {
    type: Boolean,
    default: false,
  },
  treeNodeFilterProp: {
    type: String,
    default: 'label',
  },
  // 异步加载相关配置
  loadData: {
    type: Boolean,
    default: true,
  },
  rootParentValue: {
    type: [String, Number],
    default: null,
  },
  // API 参数配置
  apiParentKey: {
    type: String,
    default: 'parentId',
  },
  // 数据模型对象，用于获取动态参数值
  model: {
    type: Object,
    default: undefined,
  },
  // 从模型中获取父ID值的字段名
  modelParentKey: {
    type: String,
    default: 'parentId',
  },
});

const emits = defineEmits(['update:value', 'optionsChange']);
const state = useVModel(props, 'value', emits, {
  defaultValue: props.value,
  passive: true,
});

const treeData = ref<TreeNode[]>([]);
const isFirstLoaded = ref<Boolean>(false);
const loading = ref(false);

watch(
  () => props.params,
  () => {
    !unref(isFirstLoaded) && fetchRootData();
  },
  { deep: true },
);

watch(
  () => props.immediate,
  (v) => {
    v && !isFirstLoaded.value && fetchRootData();
  },
);

// 监听模型变化，重新加载数据
watch(
  () => props.model,
  () => {
    if (isFirstLoaded.value) {
      isFirstLoaded.value = false;
      fetchRootData();
    }
  },
  { deep: true },
);

onMounted(() => {
  props.immediate && fetchRootData();
});

// 获取根节点的父ID值
function getRootParentValue() {
  // 如果提供了模型对象和模型字段名，从模型中获取值
  if (props.model && props.modelParentKey) {
    return get(props.model, props.modelParentKey) ?? props.rootParentValue;
  }
  // 否则使用默认的根节点父ID值
  return props.rootParentValue;
}

// 获取根节点数据
async function fetchRootData() {
  const { api } = props;
  if (!api || !isFunction(api)) return;

  loading.value = true;
  treeData.value = [];
  let result;

  try {
    // 构建根节点查询参数
    const rootParams = {
      ...props.params,
      [props.apiParentKey]: getRootParentValue(),
    };
    result = await api(rootParams);
  } catch (error) {
    console.error('Failed to fetch root data:', error);
  }
  
  loading.value = false;
  if (!result) return;
  
  if (!isArray(result)) {
    result = get(result, props.resultField);
  }
  
  treeData.value = buildTreeNodes(result);

  // 添加默认值
  if (props.defaultValue) {
    treeData.value.unshift(props.defaultValue as TreeNode);
  }

  emits('optionsChange', treeData.value);
  isFirstLoaded.value = true;
}

// 异步加载子节点数据
async function onLoadData(treeNode: any): Promise<void> {
  const { api } = props;
  if (!api || !isFunction(api)) return;
  
  // 如果已经有子节点或者是叶子节点，则不需要加载
  if (treeNode.children?.length || treeNode.isLeaf) {
    return;
  }
  
  try {
    // 构建子节点查询参数
    const childParams = {
      ...props.params,
      [props.apiParentKey]: treeNode.value,
    };
    
    const result = await api(childParams);
    let childrenData = result;
    
    if (!isArray(result)) {
      childrenData = get(result, props.resultField);
    }
    
    if (childrenData && childrenData.length > 0) {
      const childNodes = buildTreeNodes(childrenData);
      treeNode.children = childNodes;
    } else {
      // 如果没有子节点，标记为叶子节点
      treeNode.isLeaf = true;
    }
  } catch (error) {
    console.error('Failed to load child data:', error);
    treeNode.isLeaf = true;
  }
}

// 构建树节点数据
function buildTreeNodes(data: any[]): TreeNode[] {
  if (!data || !isArray(data)) return [];
  
  return data.map((item) => {
    const node: TreeNode = {
      label: item[props.labelField] || item.label,
      value: item[props.valueField] || item.value,
      isLeaf: item[props.isLeafField],
    };
    
    // 保留原始数据的其他字段
    Object.keys(item).forEach(key => {
      if (!['label', 'value', 'isLeaf', 'children'].includes(key)) {
        node[key] = item[key];
      }
    });
    
    // 如果有子节点数据，递归构建
    if (item[props.childrenKeyField] && isArray(item[props.childrenKeyField])) {
      node.children = buildTreeNodes(item[props.childrenKeyField]);
    } else if (node.isLeaf === undefined) {
      // 如果没有明确标记是否为叶子节点，默认可以展开（异步加载）
      node.isLeaf = false;
    }
    
    return node;
  });
}

// 添加默认值到树数据
function addDefaultValue() {
  if (props.defaultValue && treeData.value) {
    treeData.value.unshift(props.defaultValue as TreeNode);
  }
}

// 监听默认值变化
watch(
  () => props.defaultValue,
  () => {
    if (isFirstLoaded.value) {
      addDefaultValue();
    }
  },
  { deep: true }
);
</script>

<template>
  <TreeSelect
    v-bind="$attrs"
    v-model:value="state"
    :load-data="loadData ? onLoadData : undefined"
    :loading="loading"
    :multiple="$props.multiple"
    :placeholder="$t('common.chooseText')"
    :show-search="props.showSearch"
    :tree-data="treeData"
    :tree-node-filter-prop="props.treeNodeFilterProp"
    class="w-full"
    @dropdown-visible-change="(open) => open && !isFirstLoaded && fetchRootData()"
  />
</template>
