<template>
  <div class="p-6 space-y-6">
    <h2 class="text-xl font-semibold">ApiAsyncTreeSelect 使用示例</h2>
    
    <!-- 基本用法 -->
    <div class="space-y-4">
      <h3 class="text-lg font-medium">基本用法</h3>
      <div class="w-80">
        <ApiAsyncTreeSelect
          v-model:value="basicValue"
          :api="mockApi"
          result-field="data"
          label-field="name"
          value-field="id"
          parent-key-field="parentId"
          :root-parent-value="null"
          placeholder="请选择节点"
          show-search
          allow-clear
          @options-change="handleOptionsChange"
        />
      </div>
      <p class="text-sm text-gray-600">选中值: {{ basicValue }}</p>
    </div>

    <!-- 多选模式 -->
    <div class="space-y-4">
      <h3 class="text-lg font-medium">多选模式</h3>
      <div class="w-80">
        <ApiAsyncTreeSelect
          v-model:value="multipleValue"
          :api="mockApi"
          result-field="data"
          label-field="name"
          value-field="id"
          parent-key-field="parentId"
          :root-parent-value="null"
          placeholder="请选择多个节点"
          multiple
          show-search
          allow-clear
        />
      </div>
      <p class="text-sm text-gray-600">选中值: {{ multipleValue }}</p>
    </div>

    <!-- 带默认值 -->
    <div class="space-y-4">
      <h3 class="text-lg font-medium">带默认选项</h3>
      <div class="w-80">
        <ApiAsyncTreeSelect
          v-model:value="defaultValue"
          :api="mockApi"
          result-field="data"
          label-field="name"
          value-field="id"
          parent-key-field="parentId"
          :root-parent-value="null"
          :default-value="{ label: '根节点', value: 0 }"
          placeholder="请选择节点"
          allow-clear
        />
      </div>
      <p class="text-sm text-gray-600">选中值: {{ defaultValue }}</p>
    </div>

    <!-- 自定义参数 -->
    <div class="space-y-4">
      <h3 class="text-lg font-medium">自定义参数</h3>
      <div class="w-80">
        <ApiAsyncTreeSelect
          v-model:value="customValue"
          :api="mockApiWithParams"
          :params="{ type: 'category', status: 'active' }"
          result-field="data"
          label-field="name"
          value-field="id"
          parent-key-field="parentId"
          :root-parent-value="null"
          placeholder="请选择分类"
          allow-clear
        />
      </div>
      <p class="text-sm text-gray-600">选中值: {{ customValue }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ApiAsyncTreeSelect } from './index';

// 响应式数据
const basicValue = ref();
const multipleValue = ref([]);
const defaultValue = ref(0);
const customValue = ref();

// 模拟 API 数据
const mockData = {
  null: [ // 根节点数据
    { id: 1, name: '电子产品', parentId: null, isLeaf: false },
    { id: 2, name: '服装鞋帽', parentId: null, isLeaf: false },
    { id: 3, name: '图书音像', parentId: null, isLeaf: true },
  ],
  1: [ // 电子产品的子节点
    { id: 11, name: '手机通讯', parentId: 1, isLeaf: false },
    { id: 12, name: '电脑办公', parentId: 1, isLeaf: false },
    { id: 13, name: '家用电器', parentId: 1, isLeaf: true },
  ],
  2: [ // 服装鞋帽的子节点
    { id: 21, name: '男装', parentId: 2, isLeaf: true },
    { id: 22, name: '女装', parentId: 2, isLeaf: true },
    { id: 23, name: '童装', parentId: 2, isLeaf: true },
  ],
  11: [ // 手机通讯的子节点
    { id: 111, name: '智能手机', parentId: 11, isLeaf: true },
    { id: 112, name: '老人机', parentId: 11, isLeaf: true },
  ],
  12: [ // 电脑办公的子节点
    { id: 121, name: '笔记本', parentId: 12, isLeaf: true },
    { id: 122, name: '台式机', parentId: 12, isLeaf: true },
    { id: 123, name: '平板电脑', parentId: 12, isLeaf: true },
  ],
};

// 模拟 API 函数
async function mockApi(params: any) {
  console.log('API 请求参数:', params);
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const parentId = params.parentId;
  const data = mockData[parentId as keyof typeof mockData] || [];
  
  return {
    data,
    success: true,
  };
}

// 带自定义参数的模拟 API
async function mockApiWithParams(params: any) {
  console.log('带参数的 API 请求:', params);
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const parentId = params.parentId;
  let data = mockData[parentId as keyof typeof mockData] || [];
  
  // 根据参数过滤数据
  if (params.type === 'category') {
    data = data.filter(item => item.name.includes('产品') || item.name.includes('服装') || item.name.includes('图书'));
  }
  
  return {
    data,
    success: true,
  };
}

// 事件处理
function handleOptionsChange(options: any) {
  console.log('选项数据变化:', options);
}
</script>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
