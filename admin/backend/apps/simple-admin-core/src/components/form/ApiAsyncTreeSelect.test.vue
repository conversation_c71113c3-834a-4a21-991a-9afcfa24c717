<template>
  <div class="p-4">
    <h2 class="text-lg font-bold mb-4">ApiAsyncTreeSelect 功能测试</h2>
    
    <!-- 测试基本功能 -->
    <div class="mb-6">
      <h3 class="font-medium mb-2">1. 基本异步加载测试</h3>
      <ApiAsyncTreeSelect
        v-model:value="testValue1"
        :api="testApi"
        result-field="data"
        label-field="name"
        value-field="id"
        api-parent-key="parentId"
        :root-parent-value="null"
        placeholder="测试基本功能"
        allow-clear
      />
      <p class="text-sm mt-1">选中值: {{ testValue1 }}</p>
    </div>

    <!-- 测试自定义API参数名 -->
    <div class="mb-6">
      <h3 class="font-medium mb-2">2. 自定义API参数名测试</h3>
      <ApiAsyncTreeSelect
        v-model:value="testValue2"
        :api="testApiCustomParam"
        result-field="data"
        label-field="name"
        value-field="id"
        api-parent-key="parent_category_id"
        :root-parent-value="0"
        placeholder="测试自定义参数名"
        allow-clear
      />
      <p class="text-sm mt-1">选中值: {{ testValue2 }}</p>
    </div>

    <!-- 测试根节点值 -->
    <div class="mb-6">
      <h3 class="font-medium mb-2">3. 根节点值测试</h3>
      <ApiAsyncTreeSelect
        v-model:value="testValue3"
        :api="testApiWithRootValue"
        result-field="data"
        label-field="name"
        value-field="id"
        api-parent-key="parentId"
        :root-parent-value="100"
        placeholder="测试根节点值"
        allow-clear
      />
      <p class="text-sm mt-1">选中值: {{ testValue3 }}</p>
      <p class="text-xs text-gray-500">根节点父ID: 100</p>
    </div>

    <!-- API调用日志 -->
    <div class="mt-8">
      <h3 class="font-medium mb-2">API 调用日志</h3>
      <div class="bg-gray-100 p-3 rounded text-xs max-h-40 overflow-y-auto">
        <div v-for="(log, index) in apiLogs" :key="index" class="mb-1">
          <span class="text-blue-600">{{ log.timestamp }}</span>
          <span class="ml-2">{{ log.message }}</span>
        </div>
      </div>
      <button 
        @click="apiLogs = []"
        class="mt-2 px-3 py-1 bg-gray-500 text-white rounded text-sm"
      >
        清空日志
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ApiAsyncTreeSelect } from './index';

// 测试数据
const testValue1 = ref();
const testValue2 = ref();
const testValue3 = ref();



const apiLogs = ref<Array<{ timestamp: string; message: string }>>([]);

// 添加日志
function addLog(message: string) {
  apiLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    message,
  });
}

// 模拟数据
const mockTreeData = {
  // 基本测试数据
  basic: {
    null: [
      { id: 1, name: '根节点1', parentId: null, isLeaf: false },
      { id: 2, name: '根节点2', parentId: null, isLeaf: true },
    ],
    1: [
      { id: 11, name: '子节点1-1', parentId: 1, isLeaf: true },
      { id: 12, name: '子节点1-2', parentId: 1, isLeaf: false },
    ],
    12: [
      { id: 121, name: '子节点1-2-1', parentId: 12, isLeaf: true },
    ],
  },
  // 特殊根节点数据
  rootValue: {
    100: [
      { id: 301, name: '特殊根节点1', parentId: 100, isLeaf: false },
      { id: 302, name: '特殊根节点2', parentId: 100, isLeaf: true },
    ],
    301: [
      { id: 3011, name: '特殊子节点1', parentId: 301, isLeaf: true },
      { id: 3012, name: '特殊子节点2', parentId: 301, isLeaf: true },
    ],
  },
};

// 基本测试API
async function testApi(params: any) {
  const message = `基本API调用: ${JSON.stringify(params)}`;
  addLog(message);
  
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const parentId = params.parentId;
  const data = mockTreeData.basic[parentId as keyof typeof mockTreeData.basic] || [];
  
  return { data };
}

// 自定义参数名测试API
async function testApiCustomParam(params: any) {
  const message = `自定义参数API调用: ${JSON.stringify(params)}`;
  addLog(message);
  
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const parentId = params.parent_category_id;
  const data = mockTreeData.basic[parentId as keyof typeof mockTreeData.basic] || [];
  
  return { data };
}

// 根节点值测试API
async function testApiWithRootValue(params: any) {
  const message = `根节点值API调用: ${JSON.stringify(params)}`;
  addLog(message);

  await new Promise(resolve => setTimeout(resolve, 300));

  const parentId = params.parentId;
  const data = mockTreeData.rootValue[parentId as keyof typeof mockTreeData.rootValue] || [];

  return { data };
}
</script>

<style scoped>
.border {
  border: 1px solid #d1d5db;
}
</style>
