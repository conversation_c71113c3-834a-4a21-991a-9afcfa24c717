<template>
  <div class="p-4">
    <h2 class="text-lg font-bold mb-4">ApiAsyncTreeSelect 功能测试</h2>
    
    <!-- 测试基本功能 -->
    <div class="mb-6">
      <h3 class="font-medium mb-2">1. 基本异步加载测试</h3>
      <ApiAsyncTreeSelect
        v-model:value="testValue1"
        :api="testApi"
        result-field="data"
        label-field="name"
        value-field="id"
        api-parent-key="parentId"
        :root-parent-value="null"
        placeholder="测试基本功能"
        allow-clear
      />
      <p class="text-sm mt-1">选中值: {{ testValue1 }}</p>
    </div>

    <!-- 测试自定义API参数名 -->
    <div class="mb-6">
      <h3 class="font-medium mb-2">2. 自定义API参数名测试</h3>
      <ApiAsyncTreeSelect
        v-model:value="testValue2"
        :api="testApiCustomParam"
        result-field="data"
        label-field="name"
        value-field="id"
        api-parent-key="parent_category_id"
        :root-parent-value="0"
        placeholder="测试自定义参数名"
        allow-clear
      />
      <p class="text-sm mt-1">选中值: {{ testValue2 }}</p>
    </div>

    <!-- 测试动态模型 -->
    <div class="mb-6">
      <h3 class="font-medium mb-2">3. 动态模型测试</h3>
      <div class="mb-2">
        <label class="inline-block w-20">部门ID:</label>
        <input 
          v-model.number="testModel.departmentId" 
          type="number" 
          class="border px-2 py-1 rounded"
        />
        <button 
          @click="testModel.departmentId = testModel.departmentId === 1 ? 2 : 1"
          class="ml-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
        >
          切换部门
        </button>
      </div>
      <ApiAsyncTreeSelect
        v-model:value="testValue3"
        :api="testApiWithModel"
        result-field="data"
        label-field="name"
        value-field="id"
        api-parent-key="dept_id"
        :model="testModel"
        model-parent-key="departmentId"
        placeholder="测试动态模型"
        allow-clear
      />
      <p class="text-sm mt-1">选中值: {{ testValue3 }}</p>
      <p class="text-xs text-gray-500">当前部门ID: {{ testModel.departmentId }}</p>
    </div>

    <!-- API调用日志 -->
    <div class="mt-8">
      <h3 class="font-medium mb-2">API 调用日志</h3>
      <div class="bg-gray-100 p-3 rounded text-xs max-h-40 overflow-y-auto">
        <div v-for="(log, index) in apiLogs" :key="index" class="mb-1">
          <span class="text-blue-600">{{ log.timestamp }}</span>
          <span class="ml-2">{{ log.message }}</span>
        </div>
      </div>
      <button 
        @click="apiLogs = []"
        class="mt-2 px-3 py-1 bg-gray-500 text-white rounded text-sm"
      >
        清空日志
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ApiAsyncTreeSelect } from './index';

// 测试数据
const testValue1 = ref();
const testValue2 = ref();
const testValue3 = ref();

const testModel = reactive({
  departmentId: 1,
});

const apiLogs = ref<Array<{ timestamp: string; message: string }>>([]);

// 添加日志
function addLog(message: string) {
  apiLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    message,
  });
}

// 模拟数据
const mockTreeData = {
  // 基本测试数据
  basic: {
    null: [
      { id: 1, name: '根节点1', parentId: null, isLeaf: false },
      { id: 2, name: '根节点2', parentId: null, isLeaf: true },
    ],
    1: [
      { id: 11, name: '子节点1-1', parentId: 1, isLeaf: true },
      { id: 12, name: '子节点1-2', parentId: 1, isLeaf: false },
    ],
    12: [
      { id: 121, name: '子节点1-2-1', parentId: 12, isLeaf: true },
    ],
  },
  // 部门数据
  dept1: {
    null: [
      { id: 101, name: '技术部', parentId: null, isLeaf: false },
      { id: 102, name: '市场部', parentId: null, isLeaf: true },
    ],
    101: [
      { id: 1011, name: '前端组', parentId: 101, isLeaf: true },
      { id: 1012, name: '后端组', parentId: 101, isLeaf: true },
    ],
  },
  dept2: {
    null: [
      { id: 201, name: '销售部', parentId: null, isLeaf: false },
      { id: 202, name: '客服部', parentId: null, isLeaf: true },
    ],
    201: [
      { id: 2011, name: '华北销售', parentId: 201, isLeaf: true },
      { id: 2012, name: '华南销售', parentId: 201, isLeaf: true },
    ],
  },
};

// 基本测试API
async function testApi(params: any) {
  const message = `基本API调用: ${JSON.stringify(params)}`;
  addLog(message);
  
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const parentId = params.parentId;
  const data = mockTreeData.basic[parentId as keyof typeof mockTreeData.basic] || [];
  
  return { data };
}

// 自定义参数名测试API
async function testApiCustomParam(params: any) {
  const message = `自定义参数API调用: ${JSON.stringify(params)}`;
  addLog(message);
  
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const parentId = params.parent_category_id;
  const data = mockTreeData.basic[parentId as keyof typeof mockTreeData.basic] || [];
  
  return { data };
}

// 动态模型测试API
async function testApiWithModel(params: any) {
  const message = `动态模型API调用: ${JSON.stringify(params)}`;
  addLog(message);
  
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const deptId = params.dept_id;
  const parentId = params.parentId;
  
  const deptKey = `dept${deptId}` as keyof typeof mockTreeData;
  const deptData = mockTreeData[deptKey];
  const data = deptData?.[parentId as keyof typeof deptData] || [];
  
  return { data };
}
</script>

<style scoped>
.border {
  border: 1px solid #d1d5db;
}
</style>
