# ApiAsyncTreeSelect 组件

## 概述

`ApiAsyncTreeSelect` 是一个基于 Ant Design Vue TreeSelect 的异步树形选择组件，支持按需加载树形数据，提供灵活的配置选项。

## 新增功能

### 1. 自定义 API 参数名

通过 `apiParentKey` 属性可以自定义 API 请求中父ID参数的名称：

```vue
<ApiAsyncTreeSelect
  :api="getCategoryList"
  api-parent-key="parent_category_id"
  // API 请求参数将是: { parent_category_id: parentValue, ...otherParams }
/>
```

### 2. 根节点父ID值

通过 `rootParentValue` 属性可以指定根节点的父ID值：

```vue
<template>
  <ApiAsyncTreeSelect
    v-model:value="selectedValue"
    :api="getCategoryList"
    api-parent-key="parentId"
    :root-parent-value="null"
  />
</template>
```

## 核心特性

1. **异步加载**: 支持按需加载树形数据，提高性能
2. **灵活配置**: 支持自定义字段映射和参数配置
3. **自定义参数**: 支持自定义 API 请求参数名称
4. **兼容性**: 基于 Ant Design Vue TreeSelect，保持 API 一致性
5. **搜索功能**: 支持树节点搜索过滤

## 快速开始

### 1. 基本用法

```vue
<template>
  <ApiAsyncTreeSelect
    v-model:value="selectedValue"
    :api="getCategoryList"
    result-field="data.data"
    label-field="categoryName"
    value-field="id"
    api-parent-key="parentId"
    :root-parent-value="null"
    placeholder="请选择分类"
  />
</template>
```

### 2. 在表单 Schema 中使用

```typescript
export const formSchemas: VbenFormSchema[] = [
  {
    fieldName: 'categoryId',
    label: '商品分类',
    component: 'ApiAsyncTreeSelect',
    componentProps: {
      api: getCategoryList,
      resultField: 'data.data',
      labelField: 'categoryName',
      valueField: 'id',
      apiParentKey: 'parentId',
      rootParentValue: null,
      placeholder: '请选择分类',
      allowClear: true,
      showSearch: true,
    },
  },
];
```



## API 要求

### 请求参数格式

```typescript
// 根节点请求
{
  [apiParentKey]: rootParentValue || model[modelParentKey],
  ...otherParams
}

// 子节点请求
{
  [apiParentKey]: parentNodeValue,
  ...otherParams
}
```

### 响应数据格式

```typescript
// 直接返回数组
[
  {
    id: 1,
    name: '分类名称',
    parentId: null,
    isLeaf: false, // 可选，标识是否为叶子节点
  },
  // ...
]

// 或包装在对象中
{
  data: {
    data: [
      // 树节点数据
    ]
  }
}
```

## 文件结构

```
src/components/form/
├── ApiAsyncTreeSelect.vue      # 主组件
├── ApiAsyncTreeSelect.md       # 详细文档
├── ApiAsyncTreeSelect.example.vue  # 使用示例
├── ApiAsyncTreeSelect.test.vue     # 功能测试
├── index.ts                    # 导出文件
└── README.md                   # 本文件
```

## 注意事项

1. **性能优化**: 组件采用按需加载策略，只有在展开节点时才会加载子节点数据
2. **错误处理**: 当 API 请求失败时，会自动将节点标记为叶子节点
3. **数据缓存**: 已加载的节点数据会被缓存，避免重复请求
4. **字段映射**: 确保 API 返回的数据字段与配置的字段名匹配
5. **模型监听**: 当 `model` 对象发生变化时，组件会自动重新加载数据

## 更多示例

查看 `ApiAsyncTreeSelect.example.vue` 文件获取更多使用示例。
查看 `ApiAsyncTreeSelect.test.vue` 文件进行功能测试。
