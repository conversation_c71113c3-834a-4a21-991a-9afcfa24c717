import {
  type BaseListResp,
  type BaseResp,
  type BaseListReq,
} from '../../model/baseModel';

/**
 * 分类状态枚举
 */
export enum CategoryStatus {
  ACTIVE = 'ACTIVE',      // 启用
  INACTIVE = 'INACTIVE',  // 禁用
  DELETED = 'DELETED',    // 已删除
}

/**
 * 分类基础信息
 */
export interface CategoryInfo {
  // 分类ID
  id: number;
  // 分类编码
  categoryCode: string;
  // 分类名称
  categoryName: string;
  // 分类别名
  categoryAlias?: string;
  // 分类描述
  description?: string;
  // 父分类ID（0表示顶级分类）
  parentId: number;
  // 分类级别（1级、2级、3级等）
  level: number;
  // 分类路径
  path?: string;
  // 分类路径名称
  pathName?: string;
  // 分类图标
  icon?: string;
  // 分类图片
  image?: string;
  // 排序权重（数值越大越靠前）
  sortOrder: number;
  // 分类状态
  status: CategoryStatus;
  // 是否叶子节点
  isLeaf?: boolean;
  // 是否前台显示
  isVisible: boolean;
  // 商品数量
  productCount?: number;
  // 子分类数量
  childrenCount?: number;
  // 创建时间（时间戳）
  createdAt: number;
  // 更新时间（时间戳）
  updatedAt: number;
  // 备注
  remark?: string;

  // 兼容前端使用的字段（映射到后端字段）
  iconUrl?: string;    // 映射到 icon
  imageUrl?: string;   // 映射到 image
  sortWeight?: number; // 映射到 sortOrder
}

/**
 * 分类树形节点信息（包含子分类）
 */
export interface CategoryTreeNode extends CategoryInfo {
  // 子分类列表
  children?: CategoryTreeNode[];
  // 子分类数量
  childrenCount?: number;
  // 是否有子分类
  hasChildren?: boolean;
}

/**
 * 分类列表查询请求
 */
export interface CategoryListReq {
  page: number;
  pageSize: number;
  // 父分类ID筛选
  parentId?: number;
  // 分类状态筛选
  status?: CategoryStatus;
  // 关键词搜索（分类名称、编码）
  keyword?: string;
  // 分类级别筛选
  level?: number;
  // 是否前台显示筛选
  isVisible?: boolean;
  // 是否包含子分类数据
  includeChildren?: boolean;
  // 创建时间范围 - 开始时间
  createdAtStart?: number;
  // 创建时间范围 - 结束时间
  createdAtEnd?: number;
}

/**
 * 分类树查询请求
 */
export interface CategoryTreeReq {
  page: number;
  pageSize: number;
  // 父分类ID（获取指定分类的子树）
  parentId?: number;
  // 最大层级限制
  maxLevel?: number;
  // 分类状态筛选
  status?: CategoryStatus;
  // 是否前台显示筛选
  isVisible?: boolean;
  // 关键词搜索
  keyword?: string;
}

/**
 * 分类创建请求
 */
export interface CategoryCreateReq {
  // 分类编码
  categoryCode: string;
  // 分类名称
  categoryName: string;
  // 分类描述
  description?: string;
  // 父分类ID
  parentId: number;
  // 排序权重
  sortOrder?: number;
  // 分类状态
  status?: CategoryStatus;
  // 是否前台显示
  isVisible?: boolean;
  // 分类图标
  icon?: string;
  // 分类图片
  image?: string;
}

/**
 * 分类更新请求
 */
export interface CategoryUpdateReq {
  // 分类ID
  id: number;
  // 分类编码
  categoryCode?: string;
  // 分类名称
  categoryName?: string;
  // 分类描述
  description?: string;
  // 父分类ID
  parentId?: number;
  // 排序权重
  sortOrder?: number;
  // 分类状态
  status?: CategoryStatus;
  // 是否前台显示
  isVisible?: boolean;
  // 分类图标
  icon?: string;
  // 分类图片
  image?: string;
}

/**
 * 分类移动请求
 */
export interface CategoryMoveReq {
  // 要移动的分类ID
  categoryId: number;
  // 目标父分类ID
  targetParentId: number;
  // 目标位置（在兄弟节点中的排序）
  targetPosition?: number;
}

/**
 * 分类详情请求
 */
export interface CategoryDetailReq {
  // 分类ID
  id: number;
  // 是否包含子分类信息
  includeChildren?: boolean;
}

/**
 * 分类删除请求
 */
export interface CategoryDeleteReq {
  // 分类ID列表
  ids: number[];
  // 是否强制删除（删除包含子分类的分类）
  force?: boolean;
}

/**
 * 分类列表响应
 */
export type CategoryListResp = BaseListResp<CategoryInfo>;


export type CategoryTreeResp = BaseListResp<CategoryTreeNode>;


/**
 * 分类详情响应
 */
export interface CategoryDetailResp {
  data: CategoryTreeNode;
}

/**
 * 基础响应类型
 */
export type CategoryCreateResp = BaseResp;
export type CategoryUpdateResp = BaseResp;
export type CategoryDeleteResp = BaseResp;
export type CategoryMoveResp = BaseResp;
