<template>
  <Page auto-content-height>
    <div class="flex flex-col h-full">
      <!-- 操作栏 -->
      <div
        class="mb-4 flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <Button type="primary" @click="handleAdd"
            class="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700">
            <PlusOutlined />
            <span>新增分类</span>
          </Button>
          <Button type="primary" danger :disabled="!hasSelected" @click="handleBatchDelete"
            class="flex items-center space-x-2"
            :class="hasSelected ? 'bg-red-600 hover:bg-red-700 border-red-600 hover:border-red-700' : 'opacity-50 cursor-not-allowed'">
            <DeleteOutlined />
            <span>批量删除</span>
          </Button>
          <Button @click="handleExpandAll"
            class="flex items-center space-x-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-blue-500 hover:text-blue-600">
            <ExpandOutlined />
            <span>{{ isExpandAll ? '收起全部' : '展开全部' }}</span>
          </Button>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              已选择: {{ selectedRows.length }} 项
            </span>
            <span class="text-gray-300 dark:text-gray-600">|</span>
            <span class="text-sm text-gray-500 dark:text-gray-400">
              共 {{ totalCount }} 条记录
            </span>
          </div>
        </div>
      </div>

      <!-- 表格 -->
      <div
        class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <Grid />
      </div>
    </div>

    <!-- 分类表单弹窗 -->
    <CategoryModalComponent />
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridProps, VxeGridListeners } from '#/adapter/vxe-table';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { Button, Modal, message } from 'ant-design-vue';
import {
  PlusOutlined,
  DeleteOutlined,
  ExpandOutlined,
  EditOutlined
} from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '@vben/locales';
import CategoryModal from './CategoryModal.vue';
import { searchFormSchemas, tableColumns } from './schemas';
import {
  getCategoryTree,
  batchDeleteCategory,
  deleteCategory,
  moveCategory
} from '#/api/iot/mall/category';
import type { CategoryTreeNode } from '#/api/iot/model/categoryModel';

defineOptions({
  name: 'CategoryManagement',
});

// 状态管理
const isExpandAll = ref(false);
const totalCount = ref(0);
const selectedRows = ref<CategoryTreeNode[]>([]);

// 计算属性
const hasSelected = computed(() => selectedRows.value.length > 0);

// 弹窗组件
const [CategoryModalComponent, categoryModalApi] = useVbenModal({
  connectedComponent: CategoryModal,
  onBeforeClose: () => {
    gridApi.reload();
    return true;
  },
});

// 表单配置
const formOptions: VbenFormProps = {
  collapsed: false,
  schema: [...(searchFormSchemas.schema as any)],
  showCollapseButton: true,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-5',
};

// 表格事件
const gridEvents: VxeGridListeners = {
  checkboxChange: ({ records }) => {
    selectedRows.value = records;
  },
  checkboxAll: ({ records }) => {
    selectedRows.value = records;
  },
};

// 表格配置
const gridOptions: VxeGridProps<CategoryTreeNode> = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  stripe: true,
  treeConfig: {
    transform: true,
    rowField: 'id',
    parentField: 'parentId',
    expandAll: false,
    accordion: false,
  },
  sortConfig: {
    trigger: 'cell',
    remote: true,
  },
  toolbarConfig: {
    custom: true,
    export: false,
    import: false,
    refresh: true,
    zoom: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          const res = await getCategoryTree({
            ...formValues,
            page: page.currentPage,
            pageSize: page.pageSize,
            maxLevel: 10, // 最大显示10级
          });
          return res.data
        } catch (error) {
          console.error('获取分类数据失败:', error);
          message.error('获取分类数据失败');
          return {
            result: [],
            page: { total: 0 },
          };
        }
      },
    },
  },
};

// 初始化表格
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
  gridEvents,
});

// 工具函数：将树形数据转换为平铺数据
function flattenTreeData(treeData: CategoryTreeNode[]): CategoryTreeNode[] {
  const result: CategoryTreeNode[] = [];

  function traverse(nodes: CategoryTreeNode[], level = 1) {
    nodes.forEach(node => {
      result.push({
        ...node,
        level,
      });
      if (node.children && node.children.length > 0) {
        traverse(node.children, level + 1);
      }
    });
  }

  traverse(treeData);
  return result;
}

// 事件处理函数
function handleAdd() {
  categoryModalApi.setData({
    type: 'add',
    parentId: 0,
  });
  categoryModalApi.open();
}

function handleEdit(record: CategoryTreeNode) {
  categoryModalApi.setData({
    type: 'edit',
    record,
  });
  console.log('编辑分类数据:', record);
  categoryModalApi.open();
}

function handleAddChild(record: CategoryTreeNode) {
  categoryModalApi.setData({
    type: 'add',
    parentId: record.id,
    parentName: record.categoryName,
  });
  categoryModalApi.open();
}

function handleDelete(record: CategoryTreeNode) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${record.categoryName}"吗？`,
    onOk: async () => {
      try {
        await deleteCategory({ ids: [record.id] });
        message.success('删除成功');
        gridApi.reload();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
}

function handleBatchDelete() {
  if (!hasSelected.value) {
    message.warning('请选择要删除的分类');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRows.value.length} 个分类吗？`,
    onOk: async () => {
      try {
        const ids = selectedRows.value.map(item => item.id);
        await batchDeleteCategory(ids);
        message.success('批量删除成功');
        gridApi.reload();
        selectedRows.value = [];
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    },
  });
}

function handleExpandAll() {
  if (isExpandAll.value) {
    gridApi.grid.clearTreeExpand();
  } else {
    gridApi.grid.setAllTreeExpand(true);
  }
  isExpandAll.value = !isExpandAll.value;
}

async function handleMoveCategory(categoryId: number, targetParentId: number, targetPosition?: number) {
  try {
    await moveCategory({
      categoryId,
      targetParentId,
      targetPosition,
    });
    message.success('移动成功');
    gridApi.reload();
  } catch (error) {
    console.error('移动失败:', error);
    message.error('移动失败');
  }
}

// 暴露方法给子组件使用
defineExpose({
  handleEdit,
  handleAddChild,
  handleDelete,
});

onMounted(() => {
  // 初始加载数据
  gridApi.reload();

  // 添加事件监听器
  window.addEventListener('addChild', (event: any) => {
    handleAddChild(event.detail);
  });

  window.addEventListener('editCategory', (event: any) => {
    handleEdit(event.detail);
  });

  window.addEventListener('deleteCategory', (event: any) => {
    handleDelete(event.detail);
  });
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('addChild', () => { });
  window.removeEventListener('editCategory', () => { });
  window.removeEventListener('deleteCategory', () => { });
});
</script>

<style scoped>
/* 按钮样式优化 */
.ant-btn {
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn:hover {
  transform: scale(1.05);
}

.ant-btn:active {
  transform: scale(0.95);
}

/* 表格样式优化 */
:deep(.vxe-table) {
  border: none;
}

:deep(.vxe-table .vxe-header--row) {
  background-color: #f9fafb;
}

:deep(.dark .vxe-table .vxe-header--row) {
  background-color: #374151;
}

:deep(.vxe-table .vxe-header--column) {
  border-color: #e5e7eb;
  color: #374151;
  font-weight: 500;
}

:deep(.dark .vxe-table .vxe-header--column) {
  border-color: #4b5563;
  color: #d1d5db;
}

:deep(.vxe-table .vxe-body--row:hover) {
  background-color: #eff6ff;
  transition: background-color 0.15s;
}

:deep(.dark .vxe-table .vxe-body--row:hover) {
  background-color: #374151;
}

:deep(.vxe-table .vxe-body--column) {
  border-color: #e5e7eb;
  color: #374151;
}

:deep(.dark .vxe-table .vxe-body--column) {
  border-color: #4b5563;
  color: #d1d5db;
}

/* 树形节点样式 */
:deep(.vxe-tree--btn-wrapper) {
  color: #2563eb;
}

:deep(.dark .vxe-tree--btn-wrapper) {
  color: #60a5fa;
}

:deep(.vxe-tree--node-btn:hover) {
  background-color: #dbeafe;
  border-radius: 4px;
  transition: background-color 0.15s;
}

:deep(.dark .vxe-tree--node-btn:hover) {
  background-color: #1e3a8a;
}

/* 复选框样式 */
:deep(.vxe-checkbox--icon) {
  color: #2563eb;
}

:deep(.dark .vxe-checkbox--icon) {
  color: #60a5fa;
}

/* 分页样式 */
:deep(.vxe-pager) {
  background-color: white;
  border-top: 1px solid #e5e7eb;
}

:deep(.dark .vxe-pager) {
  background-color: #1f2937;
  border-top-color: #4b5563;
}

/* 工具栏样式 */
:deep(.vxe-toolbar) {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.dark .vxe-toolbar) {
  background-color: #374151;
  border-bottom-color: #4b5563;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .flex-col>div:first-child {
    flex-direction: column;
    gap: 0.5rem;
  }

  .flex-col>div:first-child>div:first-child {
    flex-wrap: wrap;
  }
}
</style>
