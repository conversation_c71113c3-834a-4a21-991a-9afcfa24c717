<template>
  <Modal
    :title="modalTitle"
    :width="800"
  >
    <Form />
  </Modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { message } from 'ant-design-vue';
import { dataFormSchemas } from './schemas';
import { 
  createCategory, 
  updateCategory,
  getCategoryDetail 
} from '#/api/iot/mall/category';
import type { 
  CategoryCreateReq, 
  CategoryUpdateReq,
  CategoryTreeNode 
} from '#/api/iot/model/categoryModel';

defineOptions({
  name: 'CategoryModal',
});

// 弹窗数据
const modalData = ref<{
  type: 'add' | 'edit';
  record?: CategoryTreeNode;
  parentId?: number;
  parentName?: string;
}>({
  type: 'add',
});

// 计算属性
const modalTitle = computed(() => {
  if (modalData.value.type === 'add') {
    return modalData.value.parentName 
      ? `新增子分类 - ${modalData.value.parentName}`
      : '新增分类';
  }
  return '编辑分类';
});

// 表单配置
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-4',
  schema: [...(dataFormSchemas.schema as any)],
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
});

// 弹窗配置
const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    try {
      const values = await formApi.getValues();
      const validationResult = await formApi.validate();
      
      if (!validationResult.valid) {
        message.error('请检查表单数据');
        return false;
      }

      if (modalData.value.type === 'add') {
        await handleCreate(values);
      } else {
        await handleUpdate(values);
      }

      message.success(modalData.value.type === 'add' ? '创建成功' : '更新成功');
      modalApi.close();
      return true;
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败');
      return false;
    }
  },
});

// 处理创建
async function handleCreate(values: any) {
  const createData: CategoryCreateReq = {
    categoryCode: values.categoryCode,
    categoryName: values.categoryName,
    description: values.description,
    parentId: modalData.value.parentId || values.parentId || 0,
    sortOrder: values.sortOrder || 0,
    status: values.status,
    isVisible: values.isVisible,
    icon: values.icon,
    image: values.image,
  };

  await createCategory(createData);
}

// 处理更新
async function handleUpdate(values: any) {
  if (!modalData.value.record?.id) {
    throw new Error('缺少分类ID');
  }

  const updateData: CategoryUpdateReq = {
    id: modalData.value.record.id,
    categoryCode: values.categoryCode,
    categoryName: values.categoryName,
    description: values.description,
    parentId: values.parentId,
    sortOrder: values.sortOrder,
    status: values.status,
    isVisible: values.isVisible,
    icon: values.icon,
    image: values.image,
  };

  await updateCategory(updateData);
}

// 加载分类详情
async function loadCategoryDetail(id: number) {
  try {
    const res = await getCategoryDetail({ id });
    const category = res.data.data;
    
    // 设置表单值
    await formApi.setValues({
      id: category.id,
      categoryCode: category.categoryCode,
      categoryName: category.categoryName,
      description: category.description,
      parentId: category.parentId,
      sortOrder: category.sortOrder,
      status: category.status,
      isVisible: category.isVisible,
      icon: category.icon,
      image: category.image,
    });
  } catch (error) {
    console.error('加载分类详情失败:', error);
    message.error('加载分类详情失败');
  }
}

// 监听弹窗数据变化
watch(
  () => modalData.value,
  async (newData) => {
    if (!newData) return;

    if (newData.type === 'add') {
      // 新增模式：重置表单并设置父分类ID
      await formApi.resetForm();
      if (newData.parentId) {
        await formApi.setValues({
          parentId: newData.parentId,
        });
      }
    } else if (newData.type === 'edit' && newData.record) {
      // 编辑模式：直接使用传入的record数据
      const category = newData.record;
      console.log('编辑分类数据:', category);

      const formValues = {
        id: category.id,
        categoryCode: category.categoryCode,
        categoryName: category.categoryName,
        description: category.description,
        parentId: category.parentId,
        sortOrder: category.sortOrder,
        status: category.status,
        isVisible: category.isVisible,
        icon: category.icon,
        image: category.image,
      };

      console.log('设置表单值:', formValues);

      // 添加延迟确保表单完全初始化
      await new Promise(resolve => setTimeout(resolve, 100));
      await formApi.setValues(formValues);
    }
  },
  { deep: true, immediate: true }
);

// 暴露API
defineExpose(modalApi);
</script>

<style scoped>
/* 表单项样式优化 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
  transition: all 0.2s ease-in-out;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
  color: #374151;
}

:deep(.dark .ant-form-item-label) {
  color: #d1d5db;
}

/* 输入框样式优化 */
:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select-selector),
:deep(.ant-picker),
:deep(.ant-input-number-input) {
  border-radius: 6px;
  border-color: #d1d5db;
  transition: all 0.2s ease-in-out;
}

:deep(.ant-input:focus),
:deep(.ant-input-number:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker:focus) {
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

:deep(.dark .ant-input),
:deep(.dark .ant-input-number),
:deep(.dark .ant-select-selector),
:deep(.dark .ant-picker) {
  background-color: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

:deep(.dark .ant-input:focus),
:deep(.dark .ant-input-number:focus),
:deep(.dark .ant-select-focused .ant-select-selector),
:deep(.dark .ant-picker:focus) {
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.1);
}

/* 文本域样式 */
:deep(.ant-input) {
  resize: vertical;
}

/* 错误提示样式 */
:deep(.ant-form-item-explain-error) {
  font-size: 12px;
  color: #dc2626;
}

:deep(.dark .ant-form-item-explain-error) {
  color: #f87171;
}

/* 帮助文本样式 */
:deep(.ant-form-item-extra) {
  font-size: 12px;
  color: #6b7280;
}

:deep(.dark .ant-form-item-extra) {
  color: #9ca3af;
}

/* 开关样式 */
:deep(.ant-switch) {
  background-color: #d1d5db;
}

:deep(.ant-switch-checked) {
  background-color: #2563eb;
}

:deep(.dark .ant-switch) {
  background-color: #4b5563;
}

:deep(.dark .ant-switch-checked) {
  background-color: #60a5fa;
}

/* 单选按钮样式 */
:deep(.ant-radio-button-wrapper) {
  border-color: #d1d5db;
  color: #374151;
}

:deep(.ant-radio-button-wrapper-checked) {
  background-color: #2563eb;
  border-color: #2563eb;
  color: white;
}

:deep(.dark .ant-radio-button-wrapper) {
  background-color: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

:deep(.dark .ant-radio-button-wrapper-checked) {
  background-color: #60a5fa;
  border-color: #60a5fa;
}

/* 树选择器样式 */
:deep(.ant-tree-select-dropdown) {
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

:deep(.dark .ant-tree-select-dropdown) {
  background-color: #374151;
  border-color: #4b5563;
}
</style>
