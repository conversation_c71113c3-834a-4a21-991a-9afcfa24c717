import type { VbenFormProps } from '@vben/common-ui';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { Tag, Button, Space } from 'ant-design-vue';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { z } from '#/adapter/form';
import dayjs from 'dayjs';
import { CategoryStatus } from '#/api/iot/model/categoryModel';
import { getCategoryTree, getCategoryList } from '#/api/iot/mall/category';

/**
 * 表格列配置
 */
export const tableColumns: VxeGridProps = {
  columns: [
    {
      type: 'checkbox',
      width: 50,
      fixed: 'left',
    },
    {
      field: 'categoryName',
      title: '分类名称',
      width: 200,
      fixed: 'left',
      treeNode: true,
    },
    {
      field: 'categoryCode',
      title: '分类编码',
      width: 150,
    },
    {
      field: 'level',
      title: '层级',
      width: 80,
      slots: {
        default: ({ row }: any) => {
          const levelColors = ['blue', 'green', 'orange', 'red', 'purple'];
          const color = levelColors[row.level - 1] || 'gray';
          return h(Tag, { color }, () => `${row.level}级`);
        },
      },
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      slots: {
        default: ({ row }: any) => {
          const statusConfig: Record<string, { color: string; text: string }> = {
            [CategoryStatus.ACTIVE]: { color: 'success', text: '启用' },
            [CategoryStatus.INACTIVE]: { color: 'warning', text: '禁用' },
            [CategoryStatus.DELETED]: { color: 'error', text: '已删除' },
          };
          const config = statusConfig[row.status] || { color: 'default', text: '未知' };
          return h(Tag, { color: config.color }, () => config.text);
        },
      },
    },
    {
      field: 'isVisible',
      title: '前台显示',
      width: 100,
      slots: {
        default: ({ row }: any) => {
          return h(Tag, {
            color: row.isVisible ? 'success' : 'default'
          }, () => row.isVisible ? '显示' : '隐藏');
        },
      },
    },
    {
      field: 'sortOrder',
      title: '排序',
      width: 80,
    },
    {
      field: 'description',
      title: '描述',
      showOverflow: 'tooltip',
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: ({ cellValue }: any) => {
        return cellValue ? dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      field: 'updatedAt',
      title: '更新时间',
      width: 160,
      formatter: ({ cellValue }: any) => {
        return cellValue ? dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      slots: {
        default: ({ row }: any) => {
          return h(Space, { size: 'small' }, () => [
            h(Button, {
              type: 'primary',
              size: 'small',
              icon: h(PlusOutlined),
              onClick: () => {
                // 通过事件总线触发添加子分类
                const event = new CustomEvent('addChild', { detail: row });
                window.dispatchEvent(event);
              },
            }, () => '添加子分类'),
            h(Button, {
              type: 'default',
              size: 'small',
              icon: h(EditOutlined),
              onClick: () => {
                // 通过事件总线触发编辑
                const event = new CustomEvent('editCategory', { detail: row });
                window.dispatchEvent(event);
              },
            }, () => '编辑'),
            h(Button, {
              type: 'primary',
              danger: true,
              size: 'small',
              icon: h(DeleteOutlined),
              onClick: () => {
                // 通过事件总线触发删除
                const event = new CustomEvent('deleteCategory', { detail: row });
                window.dispatchEvent(event);
              },
            }, () => '删除'),
          ]);
        },
      },
    },
  ],
};

/**
 * 搜索表单配置
 */
export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'keyword',
      label: '关键词',
      component: 'Input',
      componentProps: {
        placeholder: '请输入分类名称或编码',
        allowClear: true,
      },
    },
    {
      fieldName: 'parentId',
      label: '父分类',
      component: 'ApiAsyncTreeSelect',
      componentProps: {
        api: getCategoryList,
        resultField: 'data.data',
        params: {
          page: 1,
          pageSize: 1000,
          isVisible: true,
        },
        placeholder: '请选择父分类（不选择则为顶级分类）',
        allowClear: true,
        apiParentKey: 'parentId',
        treeDefaultExpandAll: false,
        labelField: 'categoryName',
        valueField: 'id',
        parentKeyField: 'parentId',
        rootParentValue: 0,
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          { label: '启用', value: CategoryStatus.ACTIVE },
          { label: '禁用', value: CategoryStatus.INACTIVE },
        ],
      },
    },
    {
      fieldName: 'level',
      label: '层级',
      component: 'Select',
      componentProps: {
        placeholder: '请选择层级',
        allowClear: true,
        options: [
          { label: '1级分类', value: 1 },
          { label: '2级分类', value: 2 },
          { label: '3级分类', value: 3 },
          { label: '4级分类', value: 4 },
          { label: '5级分类', value: 5 },
        ],
      },
    },
    {
      fieldName: 'isVisible',
      label: '前台显示',
      component: 'Select',
      componentProps: {
        placeholder: '请选择显示状态',
        allowClear: true,
        options: [
          { label: '显示', value: true },
          { label: '隐藏', value: false },
        ],
      },
    },
  ],
};

/**
 * 数据表单配置
 */
export const dataFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'id',
      label: 'ID',
      component: 'Input',
      dependencies: {
        show: false,
        triggerFields: ['id'],
      },
    },
    {
      fieldName: 'categoryCode',
      label: '分类编码',
      component: 'Input',
      rules: z.string().min(1, '分类编码不能为空').max(50, '分类编码不能超过50个字符'),
      componentProps: {
        placeholder: '请输入分类编码',
      },
    },
    {
      fieldName: 'categoryName',
      label: '分类名称',
      component: 'Input',
      rules: z.string().min(1, '分类名称不能为空').max(100, '分类名称不能超过100个字符'),
      componentProps: {
        placeholder: '请输入分类名称',
      },
    },
    {
      fieldName: 'parentId',
      label: '父分类',
      component: 'ApiAsyncTreeSelect',
      componentProps: {
        api: getCategoryList,
        resultField: 'data.data',
        params: {
          page: 1,
          pageSize: 1000,
          isVisible: true,
        },
        placeholder: '请选择父分类（不选择则为顶级分类）',
        allowClear: true,
        apiParentKey: 'parentId',
        treeDefaultExpandAll: false,
        labelField: 'categoryName',
        valueField: 'id',
        parentKeyField: 'parentId',
        rootParentValue: 0,
      },
    },
    {
      fieldName: 'description',
      label: '分类描述',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入分类描述',
        rows: 3,
        maxlength: 500,
        showCount: true,
      },
    },
    {
      fieldName: 'sortOrder',
      label: '排序权重',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入排序权重',
        min: 0,
        max: 9999,
      },
      defaultValue: 0,
      help: '数值越大越靠前',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '启用', value: CategoryStatus.ACTIVE },
          { label: '禁用', value: CategoryStatus.INACTIVE },
        ],
      },
      defaultValue: CategoryStatus.ACTIVE,
    },
    {
      fieldName: 'isVisible',
      label: '前台显示',
      component: 'Switch',
      componentProps: {
        checkedChildren: '显示',
        unCheckedChildren: '隐藏',
      },
      defaultValue: true,
    },
    {
      fieldName: 'icon',
      label: '分类图标',
      component: 'Input',
      componentProps: {
        placeholder: '请输入图标URL',
      },
      help: '建议使用32x32像素的图标',
    },
    {
      fieldName: 'image',
      label: '分类图片',
      component: 'Input',
      componentProps: {
        placeholder: '请输入图片URL',
      },
      help: '用于分类展示的图片',
    },
  ],
};
